from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from fastapi.responses import HTMLResponse
import asyncpg
import asyncio
from typing import List, Dict, Any
import os
from datetime import datetime
import json

app = FastAPI(title="Message History Viewer", description="API for viewing conversation message history")

# Database configuration
DB_CONFIG = {
    "host": "db",
    "port": 5432,
    "user": "postgres",
    "database": "dify"
}

async def get_db_connection():
    """Create database connection"""
    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database connection failed: {str(e)}")

async def get_messages_by_conversation_id(conversation_id: str) -> List[Dict[str, Any]]:
    """Get messages by conversation ID"""
    conn = await get_db_connection()
    try:
        query = """
        SELECT 
            id,
            conversation_id,
            query,
            message,
            message_tokens,
            answer_tokens,
            provider_response_latency,
            total_price,
            currency,
            from_source,
            from_end_user_id,
            from_account_id,
            created_at,
            updated_at,
            agent_based,
            workflow_run_id,
            status,
            error,
            message_metadata,
            invoke_from
        FROM messages 
        WHERE conversation_id = $1 
        ORDER BY created_at ASC
        """
        
        rows = await conn.fetch(query, conversation_id)
        messages = []
        
        for row in rows:
            message_data = dict(row)
            # Convert datetime objects to strings
            if message_data['created_at']:
                message_data['created_at'] = message_data['created_at'].isoformat()
            if message_data['updated_at']:
                message_data['updated_at'] = message_data['updated_at'].isoformat()
            
            # Parse JSON fields if they exist
            if message_data['message']:
                try:
                    message_data['message'] = json.loads(message_data['message']) if isinstance(message_data['message'], str) else message_data['message']
                except:
                    pass
            
            if message_data['message_metadata']:
                try:
                    message_data['message_metadata'] = json.loads(message_data['message_metadata']) if isinstance(message_data['message_metadata'], str) else message_data['message_metadata']
                except:
                    pass
                    
            messages.append(message_data)
        
        return messages
    finally:
        await conn.close()

def generate_html_response(messages: List[Dict[str, Any]], conversation_id: str) -> str:
    """Generate HTML response for messages"""
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Conversation History - {conversation_id}</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
                line-height: 1.6;
            }}
            .header {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }}
            .conversation-info {{
                background: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .message {{
                background: white;
                margin: 15px 0;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border-left: 4px solid #667eea;
            }}
            .message-header {{
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
            }}
            .message-id {{
                font-family: monospace;
                background: #f8f9fa;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.9em;
                color: #666;
            }}
            .message-time {{
                color: #666;
                font-size: 0.9em;
            }}
            .message-content {{
                margin: 10px 0;
            }}
            .query {{
                background: #e3f2fd;
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                border-left: 4px solid #2196f3;
            }}
            .answer {{
                background: #f3e5f5;
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                border-left: 4px solid #9c27b0;
            }}
            .metadata {{
                background: #f8f9fa;
                padding: 10px;
                border-radius: 5px;
                margin-top: 10px;
                font-size: 0.9em;
                color: #666;
            }}
            .status {{
                display: inline-block;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 0.8em;
                font-weight: bold;
                text-transform: uppercase;
            }}
            .status-normal {{ background: #d4edda; color: #155724; }}
            .status-error {{ background: #f8d7da; color: #721c24; }}
            .no-messages {{
                text-align: center;
                padding: 40px;
                color: #666;
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .stats {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }}
            .stat-card {{
                background: white;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .stat-number {{
                font-size: 2em;
                font-weight: bold;
                color: #667eea;
            }}
            .stat-label {{
                color: #666;
                font-size: 0.9em;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📝 Conversation History</h1>
            <p>Conversation ID: <code>{conversation_id}</code></p>
        </div>
    """
    
    if not messages:
        html_content += """
        <div class="no-messages">
            <h2>🔍 No Messages Found</h2>
            <p>No messages were found for this conversation ID.</p>
        </div>
        """
    else:
        # Add statistics
        total_messages = len(messages)
        total_tokens = sum(msg.get('message_tokens', 0) + msg.get('answer_tokens', 0) for msg in messages)
        total_cost = sum(float(msg.get('total_price', 0) or 0) for msg in messages)
        
        html_content += f"""
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_messages}</div>
                <div class="stat-label">Total Messages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{total_tokens:,}</div>
                <div class="stat-label">Total Tokens</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${total_cost:.4f}</div>
                <div class="stat-label">Total Cost</div>
            </div>
        </div>
        """
        
        # Add messages
        for i, msg in enumerate(messages, 1):
            status_class = "status-error" if msg.get('error') else "status-normal"
            status_text = "Error" if msg.get('error') else msg.get('status', 'Normal')
            
            html_content += f"""
            <div class="message">
                <div class="message-header">
                    <div>
                        <strong>Message #{i}</strong>
                        <span class="message-id">{msg.get('id', 'N/A')}</span>
                        <span class="status {status_class}">{status_text}</span>
                    </div>
                    <div class="message-time">{msg.get('created_at', 'N/A')}</div>
                </div>
                
                <div class="message-content">
                    <div class="query">
                        <strong>🤔 Query:</strong><br>
                        {msg.get('query', 'N/A')}
                    </div>
            """
            
            # Add answer if available
            if msg.get('message'):
                message_content = msg['message']
                if isinstance(message_content, dict):
                    answer = message_content.get('answer', str(message_content))
                else:
                    answer = str(message_content)
                
                html_content += f"""
                    <div class="answer">
                        <strong>🤖 Answer:</strong><br>
                        {answer}
                    </div>
                """
            
            # Add metadata
            metadata_items = []
            if msg.get('message_tokens'):
                metadata_items.append(f"Message Tokens: {msg['message_tokens']}")
            if msg.get('answer_tokens'):
                metadata_items.append(f"Answer Tokens: {msg['answer_tokens']}")
            if msg.get('total_price'):
                metadata_items.append(f"Cost: ${float(msg['total_price']):.4f}")
            if msg.get('provider_response_latency'):
                metadata_items.append(f"Latency: {msg['provider_response_latency']:.2f}s")
            if msg.get('from_source'):
                metadata_items.append(f"Source: {msg['from_source']}")
            
            if metadata_items:
                html_content += f"""
                    <div class="metadata">
                        <strong>📊 Metadata:</strong> {' | '.join(metadata_items)}
                    </div>
                """
            
            # Add error if exists
            if msg.get('error'):
                html_content += f"""
                    <div class="metadata" style="background: #f8d7da; color: #721c24;">
                        <strong>❌ Error:</strong> {msg['error']}
                    </div>
                """
            
            html_content += """
                </div>
            </div>
            """
    
    html_content += """
    </body>
    </html>
    """
    
    return html_content

@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with usage instructions"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Message History Viewer API</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .endpoint { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
            code { background: #e9ecef; padding: 2px 5px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h1>📝 Message History Viewer API</h1>
        <p>This API allows you to view conversation message history from the Dify database.</p>
        
        <h2>Available Endpoints:</h2>
        
        <div class="endpoint">
            <h3>GET /messages</h3>
            <p><strong>Description:</strong> Get messages for a specific conversation</p>
            <p><strong>Parameters:</strong></p>
            <ul>
                <li><code>conversation_id</code> (required): The conversation ID to retrieve messages for</li>
            </ul>
            <p><strong>Example:</strong> <code>/messages?conversation_id=your-conversation-id</code></p>
        </div>
        
        <div class="endpoint">
            <h3>GET /docs</h3>
            <p><strong>Description:</strong> Interactive API documentation (Swagger UI)</p>
        </div>
        
        <p><strong>Database:</strong> Connected to PostgreSQL at <code>db:5432</code> (database: dify)</p>
    </body>
    </html>
    """

@app.get("/messages", response_class=HTMLResponse)
async def get_conversation_messages(
    conversation_id: str = Query(..., description="The conversation ID to retrieve messages for")
):
    """
    Get messages for a specific conversation and display them in HTML format
    
    Args:
        conversation_id: The UUID of the conversation to retrieve messages for
    
    Returns:
        HTML page displaying the conversation messages
    """
    try:
        messages = await get_messages_by_conversation_id(conversation_id)
        html_response = generate_html_response(messages, conversation_id)
        return HTMLResponse(content=html_response)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving messages: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
