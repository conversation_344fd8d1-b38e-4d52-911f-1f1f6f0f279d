# Message History Viewer API

这是一个基于FastAPI的简单历史消息查看器，用于查看Dify数据库中的对话消息历史。

## 功能特性

- 🔍 根据conversation_id查询消息历史
- 📱 响应式HTML界面展示
- 📊 消息统计信息（总消息数、总token数、总成本）
- 🎨 美观的消息展示界面
- ⚡ 异步数据库连接，性能优异
- 📝 完整的API文档

## 快速开始

### 方法1: 使用Docker Compose（推荐）

1. 确保你有Docker和Docker Compose
2. 在项目目录中运行：

```bash
docker-compose up -d
```

3. 访问 http://localhost:8000 查看API首页

### 方法2: 直接运行

1. 安装依赖：

```bash
pip install -r requirements.txt
```

2. 确保PostgreSQL数据库可访问（host: db, port: 5432, database: dify）

3. 运行应用：

```bash
python main.py
```

或者使用uvicorn：

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## API端点

### GET /
- **描述**: API首页，显示使用说明
- **返回**: HTML页面

### GET /messages
- **描述**: 获取指定对话的消息历史
- **参数**: 
  - `conversation_id` (必需): 对话ID
- **示例**: `/messages?conversation_id=your-conversation-id-here`
- **返回**: 格式化的HTML页面，显示消息历史

### GET /docs
- **描述**: 交互式API文档（Swagger UI）
- **返回**: Swagger UI界面

## 数据库配置

应用连接到PostgreSQL数据库，配置如下：

```python
DB_CONFIG = {
    "host": "db",        # 数据库主机
    "port": 5432,        # 数据库端口
    "user": "postgres",  # 数据库用户
    "database": "dify"   # 数据库名称
}
```

## 消息展示功能

### 统计信息
- 总消息数量
- 总token使用量
- 总成本

### 消息详情
- 消息ID和时间戳
- 用户查询内容
- AI回答内容
- 元数据（token数、成本、延迟等）
- 错误信息（如果有）

### 界面特性
- 响应式设计，适配各种屏幕尺寸
- 清晰的消息分组和颜色编码
- 状态指示器（正常/错误）
- 美观的渐变色设计

## 环境变量

可以通过环境变量覆盖数据库配置：

- `DB_HOST`: 数据库主机（默认: db）
- `DB_PORT`: 数据库端口（默认: 5432）
- `DB_USER`: 数据库用户（默认: postgres）
- `DB_DATABASE`: 数据库名称（默认: dify）

## 使用示例

1. 访问首页了解API：
   ```
   http://localhost:8000/
   ```

2. 查看特定对话的消息：
   ```
   http://localhost:8000/messages?conversation_id=123e4567-e89b-12d3-a456-************
   ```

3. 查看API文档：
   ```
   http://localhost:8000/docs
   ```

## 技术栈

- **FastAPI**: 现代、快速的Web框架
- **asyncpg**: 高性能异步PostgreSQL驱动
- **uvicorn**: ASGI服务器
- **HTML/CSS**: 响应式前端界面

## 注意事项

1. 确保数据库连接配置正确
2. conversation_id必须是有效的UUID格式
3. 应用会自动处理JSON字段的解析
4. 时间戳会自动转换为ISO格式显示

## 故障排除

### 数据库连接失败
- 检查数据库是否运行
- 验证连接参数是否正确
- 确保网络连接正常

### 找不到消息
- 验证conversation_id是否正确
- 检查数据库中是否存在该对话
- 确认messages表结构是否匹配

## 开发

要在开发模式下运行：

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

这将启用热重载，代码更改时自动重启服务。
