version: '3.8'

services:
  message-history-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=db
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_DATABASE=dify
    networks:
      - dify-network
    depends_on:
      - db
    restart: unless-stopped

  # This assumes you have a PostgreSQL database running
  # If you're using an existing database, you can remove this service
  # and just connect to the external database
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: dify
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: difyai123456
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - dify-network

networks:
  dify-network:
    driver: bridge

volumes:
  postgres_data:
